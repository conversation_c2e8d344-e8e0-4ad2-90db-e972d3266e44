#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SPSS数据整理脚本 - 分析游戏交互数据
分析指标：
1. 每轮得分：ep_rewards
2. 交互：ep_actions 中的有效交互次数和无效交互次数
"""

import json
import csv
from typing import Dict, List, Tuple, Any

def load_data(file_path: str) -> Dict[str, Any]:
    """加载JSON数据文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载数据文件失败: {e}")
        return {}

def analyze_data_structure(data: Dict[str, Any]) -> None:
    """分析数据结构"""
    print("=== 数据结构分析 ===")
    print(f"数据的主要键: {list(data.keys())}")
    
    for key, value in data.items():
        if isinstance(value, list):
            print(f"{key}: 列表，长度 = {len(value)}")
            if len(value) > 0:
                print(f"  第一个元素类型: {type(value[0])}")
                if isinstance(value[0], list) and len(value[0]) > 0:
                    print(f"  第一个子元素类型: {type(value[0][0])}")
        else:
            print(f"{key}: {type(value)}")

def analyze_interactions(observations: List[List[Dict]], actions: List[List[int]] = None) -> Tuple[int, int]:
    """
    分析有效交互和无效交互次数
    
    有效交互包括：
    - 拿起/放下：食材（洋葱）、盘子、煮好的汤
    - 操作灶台：往锅里放食材、从锅里取汤
    - 提交订单：把汤送到出菜口
    
    不包括：移动、转向、无效操作（如对着空气按交互键）
    """
    valid_interactions = 0
    invalid_interactions = 0
    
    if not observations:
        return valid_interactions, invalid_interactions
    
    # 分析观察数据中的状态变化来推断交互
    for episode_idx, episode_obs in enumerate(observations):
        if len(episode_obs) < 2:
            continue
            
        for step_idx in range(1, len(episode_obs)):
            prev_obs = episode_obs[step_idx - 1]
            curr_obs = episode_obs[step_idx]
            
            # 检查玩家状态变化
            for player_idx in range(len(curr_obs.get('players', []))):
                if player_idx >= len(prev_obs.get('players', [])):
                    continue
                    
                prev_player = prev_obs['players'][player_idx]
                curr_player = curr_obs['players'][player_idx]
                
                # 检查是否拿起或放下物品
                prev_held = prev_player.get('held_object')
                curr_held = curr_player.get('held_object')
                
                if prev_held != curr_held:
                    # 物品状态发生变化，这是一个有效交互
                    if prev_held is None and curr_held is not None:
                        # 拿起物品
                        item_name = curr_held.get('name', '')
                        if item_name in ['onion', 'dish', 'soup']:
                            valid_interactions += 1
                        else:
                            invalid_interactions += 1
                    elif prev_held is not None and curr_held is None:
                        # 放下物品
                        valid_interactions += 1
                    elif prev_held is not None and curr_held is not None:
                        # 物品类型改变（比如从盘子变成汤）
                        valid_interactions += 1
            
            # 检查环境中物品状态变化（灶台操作）
            prev_objects = {(obj.get('name'), tuple(obj.get('position', []))): obj 
                          for obj in prev_obs.get('objects', [])}
            curr_objects = {(obj.get('name'), tuple(obj.get('position', []))): obj 
                          for obj in curr_obs.get('objects', [])}
            
            # 检查汤的状态变化
            for key, curr_obj in curr_objects.items():
                if key[0] == 'soup':
                    if key in prev_objects:
                        prev_soup = prev_objects[key]
                        curr_soup = curr_obj
                        
                        # 检查汤的状态是否改变（食材数量或烹饪时间）
                        prev_state = prev_soup.get('state', [])
                        curr_state = curr_soup.get('state', [])
                        
                        if prev_state != curr_state:
                            # 汤的状态发生变化，表示有交互
                            valid_interactions += 1
                    else:
                        # 新出现的汤
                        valid_interactions += 1
            
            # 检查消失的汤（可能是被提交了）
            for key, prev_obj in prev_objects.items():
                if key[0] == 'soup' and key not in curr_objects:
                    # 汤消失了，可能是被提交
                    valid_interactions += 1
    
    return valid_interactions, invalid_interactions

def extract_rewards(data: Dict[str, Any]) -> List[float]:
    """提取每轮得分"""
    rewards = []
    
    if 'ep_rewards' in data:
        ep_rewards = data['ep_rewards']
        if isinstance(ep_rewards, list):
            for episode_rewards in ep_rewards:
                if isinstance(episode_rewards, list):
                    # 计算每轮的总得分
                    total_reward = sum(episode_rewards)
                    rewards.append(total_reward)
                else:
                    rewards.append(episode_rewards)
    
    return rewards

def main():
    """主函数"""
    print("=== SPSS数据整理工具 ===")
    print("分析游戏交互数据，提取有效交互次数和得分信息\n")
    
    # 加载数据
    data = load_data('test.json')
    if not data:
        print("无法加载数据文件")
        return
    
    # 分析数据结构
    analyze_data_structure(data)
    print()
    
    # 提取观察数据
    observations = data.get('ep_observations', [])
    actions = data.get('ep_actions', [])
    
    # 分析交互
    valid_interactions, invalid_interactions = analyze_interactions(observations, actions)
    
    # 提取得分
    rewards = extract_rewards(data)
    
    # 输出结果
    print("=== 分析结果 ===")
    print(f"有效交互次数: {valid_interactions}")
    print(f"无效交互次数: {invalid_interactions}")
    print(f"总交互次数: {valid_interactions + invalid_interactions}")
    print(f"有效交互比例: {valid_interactions / (valid_interactions + invalid_interactions) * 100:.2f}%" if (valid_interactions + invalid_interactions) > 0 else "无交互数据")
    
    if rewards:
        print(f"\n每轮得分统计:")
        print(f"  轮数: {len(rewards)}")
        print(f"  总得分: {sum(rewards)}")
        print(f"  平均得分: {sum(rewards) / len(rewards):.2f}")
        print(f"  最高得分: {max(rewards)}")
        print(f"  最低得分: {min(rewards)}")
        print(f"  得分列表: {rewards}")
    else:
        print("\n未找到得分数据")
    
    # 创建SPSS格式的数据
    spss_data = {
        '有效交互次数': [valid_interactions],
        '无效交互次数': [invalid_interactions],
        '总交互次数': [valid_interactions + invalid_interactions],
        '有效交互比例': [valid_interactions / (valid_interactions + invalid_interactions) * 100 if (valid_interactions + invalid_interactions) > 0 else 0]
    }
    
    if rewards:
        spss_data.update({
            '轮数': [len(rewards)],
            '总得分': [sum(rewards)],
            '平均得分': [sum(rewards) / len(rewards)],
            '最高得分': [max(rewards)],
            '最低得分': [min(rewards)]
        })
    
    # 保存为CSV格式供SPSS使用
    with open('spss_data.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = list(spss_data.keys())
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        # 由于所有值都是单元素列表，我们需要提取第一个元素
        row_data = {key: value[0] for key, value in spss_data.items()}
        writer.writerow(row_data)

    print(f"\n已保存SPSS数据到 spss_data.csv")
    print("CSV文件内容:")
    for key, value in spss_data.items():
        print(f"{key}: {value[0]}")

if __name__ == "__main__":
    main()
