#!/usr/bin/env node
/**
 * SPSS数据整理脚本 - 分析游戏交互数据 (Node.js版本)
 * 分析指标：
 * 1. 每轮得分：ep_rewards
 * 2. 交互：ep_actions 中的有效交互次数和无效交互次数
 */

const fs = require('fs');
const path = require('path');

/**
 * 加载JSON数据文件
 * @param {string} filePath 文件路径
 * @returns {Object} 解析后的数据
 */
function loadData(filePath) {
    try {
        const rawData = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(rawData);
    } catch (error) {
        console.error(`加载数据文件失败: ${error.message}`);
        return {};
    }
}

/**
 * 分析数据结构
 * @param {Object} data 数据对象
 */
function analyzeDataStructure(data) {
    console.log("=== 数据结构分析 ===");
    console.log(`数据的主要键: ${Object.keys(data)}`);
    
    for (const [key, value] of Object.entries(data)) {
        if (Array.isArray(value)) {
            console.log(`${key}: 数组，长度 = ${value.length}`);
            if (value.length > 0) {
                console.log(`  第一个元素类型: ${typeof value[0]}`);
                if (Array.isArray(value[0]) && value[0].length > 0) {
                    console.log(`  第一个子元素类型: ${typeof value[0][0]}`);
                }
            }
        } else {
            console.log(`${key}: ${typeof value}`);
        }
    }
}

/**
 * 分析有效交互和无效交互次数
 * @param {Array} observations 观察数据
 * @param {Array} actions 动作数据
 * @returns {Object} {validInteractions, invalidInteractions}
 */
function analyzeInteractions(observations, actions = null) {
    let validInteractions = 0;
    let invalidInteractions = 0;
    
    if (!observations || !Array.isArray(observations)) {
        return { validInteractions, invalidInteractions };
    }
    
    // 分析观察数据中的状态变化来推断交互
    for (let episodeIdx = 0; episodeIdx < observations.length; episodeIdx++) {
        const episodeObs = observations[episodeIdx];
        if (!Array.isArray(episodeObs) || episodeObs.length < 2) {
            continue;
        }
        
        for (let stepIdx = 1; stepIdx < episodeObs.length; stepIdx++) {
            const prevObs = episodeObs[stepIdx - 1];
            const currObs = episodeObs[stepIdx];
            
            if (!prevObs || !currObs) continue;
            
            // 检查玩家状态变化
            const prevPlayers = prevObs.players || [];
            const currPlayers = currObs.players || [];
            
            for (let playerIdx = 0; playerIdx < currPlayers.length; playerIdx++) {
                if (playerIdx >= prevPlayers.length) continue;
                
                const prevPlayer = prevPlayers[playerIdx];
                const currPlayer = currPlayers[playerIdx];
                
                // 检查是否拿起或放下物品
                const prevHeld = prevPlayer.held_object;
                const currHeld = currPlayer.held_object;
                
                if (JSON.stringify(prevHeld) !== JSON.stringify(currHeld)) {
                    // 物品状态发生变化，这是一个有效交互
                    if (!prevHeld && currHeld) {
                        // 拿起物品
                        const itemName = currHeld.name || '';
                        if (['onion', 'dish', 'soup'].includes(itemName)) {
                            validInteractions++;
                        } else {
                            invalidInteractions++;
                        }
                    } else if (prevHeld && !currHeld) {
                        // 放下物品
                        validInteractions++;
                    } else if (prevHeld && currHeld) {
                        // 物品类型改变（比如从盘子变成汤）
                        validInteractions++;
                    }
                }
            }
            
            // 检查环境中物品状态变化（灶台操作）
            const prevObjects = new Map();
            const currObjects = new Map();
            
            (prevObs.objects || []).forEach(obj => {
                const key = `${obj.name}_${JSON.stringify(obj.position)}`;
                prevObjects.set(key, obj);
            });
            
            (currObs.objects || []).forEach(obj => {
                const key = `${obj.name}_${JSON.stringify(obj.position)}`;
                currObjects.set(key, obj);
            });
            
            // 检查汤的状态变化
            for (const [key, currObj] of currObjects) {
                if (currObj.name === 'soup') {
                    if (prevObjects.has(key)) {
                        const prevSoup = prevObjects.get(key);
                        
                        // 检查汤的状态是否改变（食材数量或烹饪时间）
                        const prevState = JSON.stringify(prevSoup.state || []);
                        const currState = JSON.stringify(currObj.state || []);
                        
                        if (prevState !== currState) {
                            // 汤的状态发生变化，表示有交互
                            validInteractions++;
                        }
                    } else {
                        // 新出现的汤
                        validInteractions++;
                    }
                }
            }
            
            // 检查消失的汤（可能是被提交了）
            for (const [key, prevObj] of prevObjects) {
                if (prevObj.name === 'soup' && !currObjects.has(key)) {
                    // 汤消失了，可能是被提交
                    validInteractions++;
                }
            }
        }
    }
    
    return { validInteractions, invalidInteractions };
}

/**
 * 提取每轮得分
 * @param {Object} data 数据对象
 * @returns {Array} 得分数组
 */
function extractRewards(data) {
    const rewards = [];
    
    if (data.ep_rewards && Array.isArray(data.ep_rewards)) {
        for (const episodeRewards of data.ep_rewards) {
            if (Array.isArray(episodeRewards)) {
                // 计算每轮的总得分
                const totalReward = episodeRewards.reduce((sum, reward) => sum + reward, 0);
                rewards.push(totalReward);
            } else {
                rewards.push(episodeRewards);
            }
        }
    }
    
    return rewards;
}

/**
 * 保存CSV文件
 * @param {Object} data 要保存的数据
 * @param {string} filename 文件名
 */
function saveCSV(data, filename) {
    const headers = Object.keys(data);
    const values = Object.values(data);
    
    let csvContent = headers.join(',') + '\n';
    
    // 假设所有值都是单个值的数组
    const maxLength = Math.max(...values.map(arr => arr.length));
    for (let i = 0; i < maxLength; i++) {
        const row = values.map(arr => arr[i] || '').join(',');
        csvContent += row + '\n';
    }
    
    fs.writeFileSync(filename, csvContent, 'utf8');
}

/**
 * 主函数
 */
function main() {
    console.log("=== SPSS数据整理工具 (Node.js版本) ===");
    console.log("分析游戏交互数据，提取有效交互次数和得分信息\n");
    
    // 加载数据
    const data = loadData('test.json');
    if (Object.keys(data).length === 0) {
        console.log("无法加载数据文件");
        return;
    }
    
    // 分析数据结构
    analyzeDataStructure(data);
    console.log();
    
    // 提取观察数据
    const observations = data.ep_observations || [];
    const actions = data.ep_actions || [];
    
    // 分析交互
    const { validInteractions, invalidInteractions } = analyzeInteractions(observations, actions);
    
    // 提取得分
    const rewards = extractRewards(data);
    
    // 输出结果
    console.log("=== 分析结果 ===");
    console.log(`有效交互次数: ${validInteractions}`);
    console.log(`无效交互次数: ${invalidInteractions}`);
    console.log(`总交互次数: ${validInteractions + invalidInteractions}`);
    
    const totalInteractions = validInteractions + invalidInteractions;
    const validRatio = totalInteractions > 0 ? (validInteractions / totalInteractions * 100).toFixed(2) : 0;
    console.log(`有效交互比例: ${validRatio}%`);
    
    if (rewards.length > 0) {
        const totalScore = rewards.reduce((sum, score) => sum + score, 0);
        const avgScore = totalScore / rewards.length;
        const maxScore = Math.max(...rewards);
        const minScore = Math.min(...rewards);
        
        console.log(`\n每轮得分统计:`);
        console.log(`  轮数: ${rewards.length}`);
        console.log(`  总得分: ${totalScore}`);
        console.log(`  平均得分: ${avgScore.toFixed(2)}`);
        console.log(`  最高得分: ${maxScore}`);
        console.log(`  最低得分: ${minScore}`);
        console.log(`  得分列表: ${JSON.stringify(rewards)}`);
    } else {
        console.log("\n未找到得分数据");
    }
    
    // 创建SPSS格式的数据
    const spssData = {
        '有效交互次数': [validInteractions],
        '无效交互次数': [invalidInteractions],
        '总交互次数': [totalInteractions],
        '有效交互比例': [parseFloat(validRatio)]
    };
    
    if (rewards.length > 0) {
        const totalScore = rewards.reduce((sum, score) => sum + score, 0);
        const avgScore = totalScore / rewards.length;
        const maxScore = Math.max(...rewards);
        const minScore = Math.min(...rewards);
        
        Object.assign(spssData, {
            '轮数': [rewards.length],
            '总得分': [totalScore],
            '平均得分': [avgScore],
            '最高得分': [maxScore],
            '最低得分': [minScore]
        });
    }
    
    // 保存为CSV格式供SPSS使用
    saveCSV(spssData, 'spss_data_node.csv');
    console.log(`\n已保存SPSS数据到 spss_data_node.csv`);
    
    // 显示CSV内容
    console.log("CSV文件内容:");
    const headers = Object.keys(spssData);
    console.log(headers.join('\t'));
    const values = Object.values(spssData).map(arr => arr[0]);
    console.log(values.join('\t'));
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    loadData,
    analyzeDataStructure,
    analyzeInteractions,
    extractRewards,
    saveCSV
};
